import { useState, useEffect, useRef } from 'react';
import { formatDateOnly, convertDateToApiFormat, convertDateFromApiFormat } from '../utils/date-format';
import { getApiSettings } from '../utils/api-settings';
import { Calendar } from 'lucide-react';

interface DateInputProps {
  value: string; // API format (YYYY-MM-DD)
  onChange: (value: string) => void; // API format (YYYY-MM-DD)
  className?: string;
  placeholder?: string;
  disabled?: boolean;
}

export const DateInput = ({ value, onChange, className, placeholder, disabled }: DateInputProps) => {
  const [displayValue, setDisplayValue] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);

  // Update display value when value prop changes
  useEffect(() => {
    if (value) {
      const formatted = convertDateFromApiFormat(value);
      setDisplayValue(formatted);
    } else {
      setDisplayValue('');
    }
  }, [value]);

  const handleFocus = () => {
    setIsEditing(true);
    // Convert to YYYY-MM-DD for HTML date input
    setInputValue(value || '');
  };

  const handleBlur = () => {
    setIsEditing(false);
    // Update display value
    if (value) {
      const formatted = convertDateFromApiFormat(value);
      setDisplayValue(formatted);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    onChange(newValue); // Already in YYYY-MM-DD format
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' || e.key === 'Escape') {
      inputRef.current?.blur();
    }
  };

  const getPlaceholderText = () => {
    if (placeholder) return placeholder;

    const settings = getApiSettings();
    const dateFormat = settings.dateFormat || "DD.MM.YYYY HH:mm:ss";

    // Extract only the date part from the format
    const dateOnlyFormat = dateFormat
      .replace(/\s*HH:mm:ss/g, '')
      .replace(/\s*HH:mm/g, '')
      .replace(/\s*hh:mm:ss\s*A/g, '')
      .replace(/\s*hh:mm:ss\s*a/g, '')
      .replace(/\s*hh:mm\s*A/g, '')
      .replace(/\s*hh:mm\s*a/g, '')
      .trim();

    return dateOnlyFormat.toLowerCase();
  };

  if (isEditing) {
    return (
      <input
        ref={inputRef}
        type="date"
        value={inputValue}
        onChange={handleChange}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        className={`${className} date-picker-fixed`}
        disabled={disabled}
        autoFocus
      />
    );
  }

  return (
    <div className="relative">
      <input
        type="text"
        value={displayValue}
        onFocus={handleFocus}
        placeholder={getPlaceholderText()}
        className={`${className} cursor-pointer pr-10`}
        disabled={disabled}
        readOnly
      />
      <Calendar
        className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none"
      />
    </div>
  );
};
