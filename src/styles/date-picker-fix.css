/* Fix for date picker positioning issue */
input[type="date"]::-webkit-calendar-picker-indicator {
  position: relative;
  z-index: 1;
}

/* Ensure date picker dropdown is properly positioned */
input[type="date"] {
  position: relative;
  z-index: auto;
}

/* Fix for webkit date picker */
input[type="date"]::-webkit-datetime-edit {
  position: relative;
}

/* Prevent date picker from moving with scroll */
input[type="date"]:focus {
  position: relative;
  z-index: 1000;
}

/* Additional fix for date picker calendar */
input[type="date"]::-webkit-calendar-picker-indicator:hover {
  cursor: pointer;
}

/* Ensure proper stacking context */
.date-input-container {
  position: relative;
  z-index: auto;
}

/* Fix for date picker in modal/overlay contexts */
.date-picker-fixed {
  position: relative !important;
  z-index: auto !important;
}

/* Prevent calendar from inheriting transform properties */
input[type="date"]::-webkit-calendar-picker-indicator {
  transform: none !important;
}
