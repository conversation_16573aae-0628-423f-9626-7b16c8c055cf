/* Fix for date picker positioning issue */
input[type="date"]::-webkit-calendar-picker-indicator {
  position: relative;
  z-index: 1;
  cursor: pointer;
  opacity: 1;
  background: transparent;
  color: #6b7280; /* gray-500 */
  width: 16px;
  height: 16px;
}

/* Ensure date picker dropdown is properly positioned */
input[type="date"] {
  position: relative;
  z-index: auto;
}

/* Fix for webkit date picker */
input[type="date"]::-webkit-datetime-edit {
  position: relative;
}

/* Prevent date picker from moving with scroll */
input[type="date"]:focus {
  position: relative;
  z-index: 1000;
}

/* Additional fix for date picker calendar */
input[type="date"]::-webkit-calendar-picker-indicator:hover {
  cursor: pointer;
  opacity: 0.8;
}

/* Dark mode support for calendar icon */
.dark input[type="date"]::-webkit-calendar-picker-indicator {
  color: #9ca3af; /* gray-400 */
  filter: invert(1);
}

/* Ensure proper stacking context */
.date-input-container {
  position: relative;
  z-index: auto;
}

/* Fix for date picker in modal/overlay contexts */
.date-picker-fixed {
  position: relative !important;
  z-index: auto !important;
}

/* Prevent calendar from inheriting transform properties */
input[type="date"]::-webkit-calendar-picker-indicator {
  transform: none !important;
}

/* Ensure calendar icon is visible and properly styled */
input[type="date"]::-webkit-calendar-picker-indicator {
  background-image: none;
  background-size: 16px 16px;
  background-repeat: no-repeat;
  background-position: center;
}
