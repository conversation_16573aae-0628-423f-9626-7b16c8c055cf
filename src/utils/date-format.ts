import { getApiSettings } from "./api-settings";

export function formatDate(dateString: string): string {
  const settings = getApiSettings();
  const dateFormat = settings.dateFormat || "DD.MM.YYYY HH:mm:ss";
  const is12HourFormat = dateFormat.includes('hh:');
  const timezone = settings.timezone || "Europe/Kyiv";

  const options: Intl.DateTimeFormatOptions = {
    timeZone: timezone,
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    hour12: is12HourFormat,
  };

  try {
    const date = new Date(dateString);
    const formatter = new Intl.DateTimeFormat("en-US", options);
    const parts = formatter.formatToParts(date);
    const values: { [key: string]: string } = {};

    parts.forEach((part) => {
      values[part.type] = part.value;
    });

    return dateFormat
      .replace("DD", values.day)
      .replace("MM", values.month)
      .replace("YYYY", values.year)
      .replace("HH", values.hour)
      .replace("hh", values.hour)
      .replace("mm", values.minute)
      .replace("ss", values.second)
      .replace("A", values.dayPeriod || "")
      .replace("a", (values.dayPeriod || "").toLowerCase());
  } catch (error) {
    console.error("Error formatting date:", error);
    return dateString;
  }
}

export function formatDateOnly(dateString: string): string {
  const settings = getApiSettings();
  const dateFormat = settings.dateFormat || "DD.MM.YYYY HH:mm:ss";
  const timezone = settings.timezone || "Europe/Kyiv";

  // Extract only the date part from the format (remove time components)
  const dateOnlyFormat = dateFormat
    .replace(/\s*HH:mm:ss/g, "")
    .replace(/\s*HH:mm/g, "")
    .replace(/\s*hh:mm:ss\s*A/g, "")
    .replace(/\s*hh:mm:ss\s*a/g, "")
    .replace(/\s*hh:mm\s*A/g, "")
    .replace(/\s*hh:mm\s*a/g, "")
    .trim();

  const options: Intl.DateTimeFormatOptions = {
    timeZone: timezone,
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  };

  try {
    const date = new Date(dateString);
    const formatter = new Intl.DateTimeFormat("en-US", options);
    const parts = formatter.formatToParts(date);
    const values: { [key: string]: string } = {};

    parts.forEach((part) => {
      values[part.type] = part.value;
    });

    return dateOnlyFormat
      .replace("DD", values.day)
      .replace("MM", values.month)
      .replace("YYYY", values.year);
  } catch (error) {
    console.error("Error formatting date:", error);
    return dateString;
  }
}

// Convert date from user format to API format (YYYY-MM-DD)
export function convertDateToApiFormat(dateString: string, userFormat?: string): string {
  if (!dateString) return "";

  const settings = getApiSettings();
  const format = userFormat || settings.dateFormat || "DD.MM.YYYY HH:mm:ss";

  // Extract only the date part from the format
  const dateOnlyFormat = format
    .replace(/\s*HH:mm:ss/g, "")
    .replace(/\s*HH:mm/g, "")
    .replace(/\s*hh:mm:ss\s*A/g, "")
    .replace(/\s*hh:mm:ss\s*a/g, "")
    .replace(/\s*hh:mm\s*A/g, "")
    .replace(/\s*hh:mm\s*a/g, "")
    .trim();

  try {
    let day: string, month: string, year: string;

    if (dateOnlyFormat === "DD.MM.YYYY") {
      const parts = dateString.split(".");
      if (parts.length === 3) {
        day = parts[0];
        month = parts[1];
        year = parts[2];
      } else {
        throw new Error("Invalid date format");
      }
    } else if (dateOnlyFormat === "MM/DD/YYYY") {
      const parts = dateString.split("/");
      if (parts.length === 3) {
        month = parts[0];
        day = parts[1];
        year = parts[2];
      } else {
        throw new Error("Invalid date format");
      }
    } else if (dateOnlyFormat === "YYYY-MM-DD") {
      // Already in API format
      return dateString;
    } else {
      // Try to parse as a standard date
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        throw new Error("Invalid date");
      }
      year = date.getFullYear().toString();
      month = (date.getMonth() + 1).toString().padStart(2, "0");
      day = date.getDate().toString().padStart(2, "0");
    }

    // Ensure proper padding
    day = day.padStart(2, "0");
    month = month.padStart(2, "0");

    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error("Error converting date to API format:", error);
    return dateString;
  }
}

// Convert date from API format (YYYY-MM-DD) to user format
export function convertDateFromApiFormat(dateString: string, userFormat?: string): string {
  if (!dateString) return "";

  const settings = getApiSettings();
  const format = userFormat || settings.dateFormat || "DD.MM.YYYY HH:mm:ss";

  // Extract only the date part from the format
  const dateOnlyFormat = format
    .replace(/\s*HH:mm:ss/g, "")
    .replace(/\s*HH:mm/g, "")
    .replace(/\s*hh:mm:ss\s*A/g, "")
    .replace(/\s*hh:mm:ss\s*a/g, "")
    .replace(/\s*hh:mm\s*A/g, "")
    .replace(/\s*hh:mm\s*a/g, "")
    .trim();

  try {
    // Parse API format (YYYY-MM-DD)
    const parts = dateString.split("-");
    if (parts.length !== 3) {
      throw new Error("Invalid API date format");
    }

    const year = parts[0];
    const month = parts[1];
    const day = parts[2];

    return dateOnlyFormat.replace("DD", day).replace("MM", month).replace("YYYY", year);
  } catch (error) {
    console.error("Error converting date from API format:", error);
    return dateString;
  }
}
